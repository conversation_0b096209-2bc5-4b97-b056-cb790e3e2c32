import Link from 'next/link'

export function Header() {
  return (
    <header className="border-b border-gray-200 bg-white">
      <div className="max-w-4xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          <div>
            <Link 
              href="/" 
              className="text-2xl font-bold text-gray-900 hover:text-gray-700 transition-colors"
            >
              LinkNote
            </Link>
          </div>
          
          <nav className="flex items-center space-x-8">
            <Link 
              href="/blog" 
              className="text-gray-700 hover:text-gray-900 transition-colors font-medium"
            >
              博客
            </Link>
            <Link 
              href="/about" 
              className="text-gray-700 hover:text-gray-900 transition-colors font-medium"
            >
              关于
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
