'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { Toaster, toast } from 'sonner'
import {
  Loader2,
  ImageIcon,
  Monitor,
  Tablet,
  Smartphone,
  Download,
  X
} from 'lucide-react'

type DeviceType = 'desktop' | 'tablet' | 'mobile'

export default function ParsePage() {
  const [url, setUrl] = useState('')
  const [device, setDevice] = useState<DeviceType>('desktop')
  const [screenshot, setScreenshot] = useState('')
  const [loading, setLoading] = useState(false)

  const handleCapture = async () => {
    if (!url) {
      toast.error('Please enter a URL.')
      return
    }
    setLoading(true)
    setScreenshot('')
    try {
      const response = await fetch(
        `/api/screenshot?url=${encodeURIComponent(url)}&device=${device}`
      )
      if (response.ok) {
        const imageBlob = await response.blob()
        const imageUrl = URL.createObjectURL(imageBlob)
        setScreenshot(imageUrl)
        toast.success('Screenshot captured successfully!')
      } else {
        const errorText = await response.text()
        toast.error(`Failed to capture screenshot: ${errorText}`)
      }
    } catch (error) {
      console.error('Error capturing screenshot:', error)
      toast.error('An error occurred while capturing the screenshot.')
    } finally {
      setLoading(false)
    }
  }

  const handleClear = () => {
    setUrl('')
    setScreenshot('')
  }

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = screenshot
    link.download = `screenshot-${device}-${new Date().toISOString()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 md:p-8">
      <Toaster
        richColors
        position="top-center"
      />
      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Create a Website Snapshot</CardTitle>
            <CardDescription>
              Enter a URL and select a device to generate a full-page
              screenshot.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="relative w-full">
                <Input
                  type="url"
                  placeholder="https://example.com"
                  value={url}
                  onChange={e => setUrl(e.target.value)}
                  onKeyDown={e => e.key === 'Enter' && handleCapture()}
                  disabled={loading}
                  className="pr-10"
                />
                {url && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={handleClear}
                    disabled={loading}>
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Select
                  onValueChange={(value: DeviceType) => setDevice(value)}
                  defaultValue={device}
                  disabled={loading}>
                  <SelectTrigger className="w-full sm:w-[150px]">
                    <SelectValue placeholder="Select device" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desktop">
                      <div className="flex items-center">
                        <Monitor className="mr-2 h-4 w-4" />
                        Desktop
                      </div>
                    </SelectItem>
                    <SelectItem value="tablet">
                      <div className="flex items-center">
                        <Tablet className="mr-2 h-4 w-4" />
                        Tablet
                      </div>
                    </SelectItem>
                    <SelectItem value="mobile">
                      <div className="flex items-center">
                        <Smartphone className="mr-2 h-4 w-4" />
                        Mobile
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleCapture}
                  disabled={loading}
                  className="w-full sm:w-auto">
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Capture
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <Card>
            <CardHeader>
              <CardTitle>Generating Preview...</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="w-full h-96" />
            </CardContent>
          </Card>
        ) : screenshot ? (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Screenshot Preview</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <img
                src={screenshot}
                alt="Screenshot"
                className="w-full rounded-md border"
              />
            </CardContent>
          </Card>
        ) : (
          <Card className="flex items-center justify-center border-2 border-dashed bg-muted/50 h-96">
            <div className="text-center">
              <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-4 text-sm text-muted-foreground">
                Your generated screenshot will appear here.
              </p>
            </div>
          </Card>
        )}
      </div>
    </div>
  )
}
