import { NextResponse } from 'next/server'
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, Viewport } from 'puppeteer'

// --- Device Presets ---
const devices: Record<string, { viewport: Viewport; userAgent: string }> = {
  mobile: {
    viewport: { width: 390, height: 844, deviceScaleFactor: 3 },
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1'
  },
  tablet: {
    viewport: { width: 1024, height: 1366, deviceScaleFactor: 2 },
    userAgent:
      'Mozilla/5.0 (iPad; CPU OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1'
  },
  desktop: {
    viewport: { width: 1920, height: 1080 },
    userAgent:
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  }
}
type DeviceType = keyof typeof devices

// --- Browser Caching ---
let browser: Browser | null = null

async function getBrowser() {
  if (browser && browser.isConnected()) {
    return browser
  }
  if (browser) {
    await browser.close().catch(e => console.error('Error closing browser:', e))
  }
  browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage'
    ]
  })
  browser.on('disconnected', () => {
    console.log('Browser disconnected.')
    browser = null
  })
  return browser
}

// --- API Route ---
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const url = searchParams.get('url')
  const device = (searchParams.get('device') as DeviceType) || 'desktop'

  if (!url) {
    return new NextResponse('URL is required', { status: 400 })
  }

  const settings = devices[device] || devices.desktop
  let page: Page | undefined

  try {
    const browser = await getBrowser()
    page = await browser.newPage()

    await page.setUserAgent(settings.userAgent)
    await page.setViewport(settings.viewport)

    await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 })

    const screenshot = await page.screenshot({ type: 'png', fullPage: true })

    return new NextResponse(screenshot, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    })
  } catch (error) {
    console.error(`Failed to capture screenshot for URL: ${url}`, error)
    if (browser && !browser.isConnected()) {
      await browser
        .close()
        .catch(e => console.error('Error closing crashed browser:', e))
      browser = null
    }
    return new NextResponse('Failed to capture screenshot', { status: 500 })
  } finally {
    if (page) {
      try {
        await page.close()
      } catch (e) {
        console.error(
          'Error closing page, it might have already been closed:',
          e
        )
      }
    }
  }
}
