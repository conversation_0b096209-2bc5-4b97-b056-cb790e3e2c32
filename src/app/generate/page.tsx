/*
  1. 根据文字生成图片
  2. 不同的颜色主题
  3. 不同的模板

  根据中/英文调用翻译API 生成图片
*/
'use client'
import { useRef } from 'react'
import { RenderCanvas } from './components/RenderCanvas'

const renderList = [
  {
    type: 'Text',
    source: 'Hello, world!',
    position: { x: 100, y: 100 }
  }
]

export default function GeneratePage() {
  const renderCanvasRef = useRef<RenderCanvasRef>(null)

  return (
    <div>
      <RenderCanvas
        width={400}
        height={300}
        ref={renderCanvasRef}
        renderList={renderList}
      />
    </div>
  )
}
