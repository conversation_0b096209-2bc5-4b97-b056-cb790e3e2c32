'use client'

import React, {
  useRef,
  useEffect,
  useState,
  useMemo,
  useCallback,
  forwardRef,
  useImperativeHandle
} from 'react'

interface RenderItem {
  type: string
  source?: any
  position: {
    x?: number
    y?: number
    width?: number | string
    height?: number | string
  }
  style?: any
}

interface Props {
  renderList?: RenderItem[]
  width?: number | string
  height?: number | string
  pixelRatio?: number | null
  loadingText?: string
  onBeforeRender?: () => void
  onRenderSuccess?: () => void
  onRenderError?: (error: any) => void
  onRenderFinish?: () => void
}

interface RenderCanvasRef {
  clear: () => void
  getPixelRatio: () => number
  exportDataURL: (type?: string, quality?: number) => string | null
  exportBlobURL: (type?: string, quality?: number) => Promise<Blob>
  downloadImage: (
    filename?: string,
    type?: string,
    quality?: number
  ) => Promise<boolean>
  render: () => Promise<void>
}

const lifeCycleMap = {
  beforeRender: 'beforeRender',
  renderSuccess: 'renderSuccess',
  renderError: 'renderError',
  renderFinish: 'renderFinish'
}

export const RenderCanvas = forwardRef<RenderCanvasRef, Props>(
  (
    {
      renderList = [],
      width = 800,
      height = 600,
      pixelRatio = null,
      loadingText = '正在渲染中...',
      onBeforeRender,
      onRenderSuccess,
      onRenderError,
      onRenderFinish
    },
    ref
  ) => {
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const [canvas, setCanvas] = useState<HTMLCanvasElement | null>(null)
    const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null)
    const [loadedImages] = useState(new Map<string, HTMLImageElement>())
    const [actualPixelRatio, setActualPixelRatio] = useState(1)
    const [isLoading, setIsLoading] = useState(false)

    const canvasWidth = useMemo(() => {
      return typeof width === 'string' ? parseInt(width) : width
    }, [width])

    const canvasHeight = useMemo(() => {
      return typeof height === 'string' ? parseInt(height) : height
    }, [height])

    const canvasStyle = useMemo(() => {
      return {
        width: canvasWidth + 'px',
        height: canvasHeight + 'px'
      }
    }, [canvasWidth, canvasHeight])

    const parsePosition = useCallback(
      (position: any) => {
        const { x, y } = position
        let { width: w, height: h } = position

        // 处理百分比宽度
        if (typeof w === 'string' && w.includes('%')) {
          w = (parseInt(w) / 100) * canvasWidth
        }

        // 处理百分比高度
        if (typeof h === 'string' && h.includes('%')) {
          h = (parseInt(h) / 100) * canvasHeight
        }

        return {
          x: x || 0,
          y: y || 0,
          width: w || 0,
          height: h || 0
        }
      },
      [canvasWidth, canvasHeight]
    )

    const setCommonStyles = useCallback(
      (style: any = {}) => {
        if (!ctx) return

        // 设置填充颜色
        if (style.fillStyle) {
          ctx.fillStyle = style.fillStyle
        }

        // 设置描边样式
        if (style.strokeStyle) {
          ctx.strokeStyle = style.strokeStyle
        }

        if (style.lineWidth) {
          ctx.lineWidth = style.lineWidth
        }

        // 设置阴影
        if (style.shadowColor) {
          ctx.shadowColor = style.shadowColor || 'rgba(0,0,0,0.5)'
          ctx.shadowBlur = style.shadowBlur || 3
          ctx.shadowOffsetX = style.shadowOffsetX || 2
          ctx.shadowOffsetY = style.shadowOffsetY || 2
        }

        // 设置透明度
        if (style.globalAlpha !== undefined) {
          ctx.globalAlpha = style.globalAlpha
        }

        // 设置混合模式
        if (style.globalCompositeOperation) {
          ctx.globalCompositeOperation = style.globalCompositeOperation
        }
      },
      [ctx]
    )

    const drawImage = useCallback(
      (img: HTMLImageElement, position: any) => {
        if (!ctx) return

        const { x, y, width: w, height: h } = parsePosition(position)

        try {
          // 保存当前绘图状态
          ctx.save()

          // 设置图像渲染质量
          ctx.imageSmoothingEnabled = true
          ctx.imageSmoothingQuality = 'high'

          ctx.drawImage(img, x, y, w, h)

          // 恢复绘图状态
          ctx.restore()
        } catch (error) {
          console.error('绘制图片失败:', error)
        }
      },
      [ctx, parsePosition]
    )

    const renderImage = useCallback(
      (src: string, position: any) => {
        // 直接从缓存中获取已加载的图片
        if (loadedImages.has(src)) {
          const img = loadedImages.get(src)!
          drawImage(img, position)
        } else {
          console.warn('图片未加载:', src)
        }
      },
      [loadedImages, drawImage]
    )

    const renderText = useCallback(
      (text: string, position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y } = parsePosition(position)

        // 保存当前绘图状态
        ctx.save()

        // 设置文字样式
        ctx.fillStyle = style.fillStyle || '#000000'
        ctx.font = style.font || '16px Arial'
        ctx.textAlign = style.textAlign || 'left'
        ctx.textBaseline = style.textBaseline || 'top'

        // 设置描边样式
        if (style.strokeStyle) {
          ctx.strokeStyle = style.strokeStyle
          ctx.lineWidth = style.lineWidth || 1
          ctx.strokeText(text, x, y)
        }

        // 设置阴影
        if (style.shadowColor) {
          ctx.shadowColor = style.shadowColor || 'rgba(0,0,0,0.5)'
          ctx.shadowBlur = style.shadowBlur || 3
          ctx.shadowOffsetX = style.shadowOffsetX || 2
          ctx.shadowOffsetY = style.shadowOffsetY || 2
        }

        // 绘制文字
        ctx.fillText(text, x, y)

        // 恢复绘图状态
        ctx.restore()
      },
      [ctx, parsePosition]
    )

    const renderRectangle = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y, width: w, height: h } = parsePosition(position)

        ctx.save()

        // 设置样式
        setCommonStyles(style)

        // 绘制矩形
        if (style.fill !== false) {
          ctx.fillRect(x, y, w, h)
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.strokeRect(x, y, w, h)
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderCircle = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y, width: w } = parsePosition(position)
        const radius = w / 2

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()
        ctx.arc(x + radius, y + radius, radius, 0, 2 * Math.PI)

        if (style.fill !== false) {
          ctx.fill()
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.stroke()
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderLine = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y, width: w, height: h } = parsePosition(position)

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()
        ctx.moveTo(x, y)
        ctx.lineTo(x + w, y + h)

        // 设置线条样式
        ctx.lineCap = style.lineCap || 'butt'
        ctx.lineJoin = style.lineJoin || 'miter'

        if (style.lineDash) {
          ctx.setLineDash(style.lineDash)
        }

        ctx.stroke()
        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderPath = useCallback(
      (pathData: any[], position: any, style: any = {}) => {
        if (!ctx || !pathData || !Array.isArray(pathData)) return

        const { x: offsetX, y: offsetY } = parsePosition(position)

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()

        pathData.forEach(command => {
          const { type, points = [] } = command
          const adjustedPoints = points.map((point: number, i: number) =>
            i % 2 === 0 ? point + offsetX : point + offsetY
          )

          switch (type) {
            case 'moveTo':
              ctx.moveTo(adjustedPoints[0], adjustedPoints[1])
              break
            case 'lineTo':
              ctx.lineTo(adjustedPoints[0], adjustedPoints[1])
              break
            case 'quadraticCurveTo':
              ctx.quadraticCurveTo(
                adjustedPoints[0],
                adjustedPoints[1],
                adjustedPoints[2],
                adjustedPoints[3]
              )
              break
            case 'bezierCurveTo':
              ctx.bezierCurveTo(
                adjustedPoints[0],
                adjustedPoints[1],
                adjustedPoints[2],
                adjustedPoints[3],
                adjustedPoints[4],
                adjustedPoints[5]
              )
              break
            case 'arc':
              ctx.arc(
                adjustedPoints[0],
                adjustedPoints[1],
                points[2],
                points[3],
                points[4],
                points[5] || false
              )
              break
            case 'closePath':
              ctx.closePath()
              break
          }
        })

        if (style.fill !== false) {
          ctx.fill()
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.stroke()
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderGradient = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx || !style.gradient) return

        const { x, y, width: w, height: h } = parsePosition(position)

        ctx.save()

        let gradient: CanvasGradient
        const { type, colors = [], stops = [] } = style.gradient

        switch (type) {
          case 'linear':
            const { x1 = 0, y1 = 0, x2 = w, y2 = 0 } = style.gradient
            gradient = ctx.createLinearGradient(x + x1, y + y1, x + x2, y + y2)
            break
          case 'radial':
            const {
              cx = w / 2,
              cy = h / 2,
              r = Math.min(w, h) / 2
            } = style.gradient
            gradient = ctx.createRadialGradient(
              x + cx,
              y + cy,
              0,
              x + cx,
              y + cy,
              r
            )
            break
          default:
            gradient = ctx.createLinearGradient(x, y, x + w, y)
        }

        // 添加色标
        colors.forEach((color: string, index: number) => {
          const stop = stops[index] || index / (colors.length - 1)
          gradient.addColorStop(stop, color)
        })

        ctx.fillStyle = gradient
        ctx.fillRect(x, y, w, h)

        ctx.restore()
      },
      [ctx, parsePosition]
    )

    const renderPolygon = useCallback(
      (points: number[][], position: any, style: any = {}) => {
        if (!ctx || !points || !Array.isArray(points) || points.length < 3)
          return

        const { x: offsetX, y: offsetY } = parsePosition(position)

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()
        ctx.moveTo(points[0][0] + offsetX, points[0][1] + offsetY)

        for (let i = 1; i < points.length; i++) {
          ctx.lineTo(points[i][0] + offsetX, points[i][1] + offsetY)
        }

        ctx.closePath()

        if (style.fill !== false) {
          ctx.fill()
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.stroke()
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderArc = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y, width: w } = parsePosition(position)
        const radius = w / 2
        const startAngle = ((style.startAngle || 0) * Math.PI) / 180
        const endAngle = ((style.endAngle || 360) * Math.PI) / 180

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()
        ctx.arc(
          x + radius,
          y + radius,
          radius,
          startAngle,
          endAngle,
          style.counterclockwise || false
        )

        if (style.fill !== false) {
          ctx.fill()
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.stroke()
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderEllipse = useCallback(
      (position: any, style: any = {}) => {
        if (!ctx) return

        const { x, y, width: w, height: h } = parsePosition(position)
        const centerX = x + w / 2
        const centerY = y + h / 2
        const radiusX = w / 2
        const radiusY = h / 2

        ctx.save()
        setCommonStyles(style)

        ctx.beginPath()
        ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI)

        if (style.fill !== false) {
          ctx.fill()
        }

        if (style.stroke !== false && (style.strokeStyle || style.lineWidth)) {
          ctx.stroke()
        }

        ctx.restore()
      },
      [ctx, parsePosition, setCommonStyles]
    )

    const renderItem = useCallback(
      (item: RenderItem) => {
        const { type, source, position, style = {} } = item

        switch (type) {
          case 'Image':
            renderImage(source, position)
            break
          case 'Text':
            renderText(source, position, style)
            break
          case 'Rectangle':
            renderRectangle(position, style)
            break
          case 'Circle':
            renderCircle(position, style)
            break
          case 'Line':
            renderLine(position, style)
            break
          case 'Path':
            renderPath(source, position, style)
            break
          case 'Gradient':
            renderGradient(position, style)
            break
          case 'Polygon':
            renderPolygon(source, position, style)
            break
          case 'Arc':
            renderArc(position, style)
            break
          case 'Ellipse':
            renderEllipse(position, style)
            break
          default:
            console.warn(`未知的渲染类型: ${type}`)
        }
      },
      [
        renderImage,
        renderText,
        renderRectangle,
        renderCircle,
        renderLine,
        renderPath,
        renderGradient,
        renderPolygon,
        renderArc,
        renderEllipse
      ]
    )

    const renderAllItems = useCallback(() => {
      if (!ctx) return

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight)

      // 按照 renderList 的顺序渲染所有元素
      renderList.forEach(item => {
        renderItem(item)
      })
    }, [ctx, canvasWidth, canvasHeight, renderList, renderItem])

    const loadImages = useCallback(
      async (imageItems: RenderItem[]) => {
        const imagePromises = imageItems.map(item => {
          return new Promise<void>((resolve, reject) => {
            const src = item.source

            // 检查是否已经加载过该图片
            if (loadedImages.has(src)) {
              resolve()
              return
            }

            // 创建新的图片对象
            const img = new Image()
            img.crossOrigin = 'anonymous'

            img.onload = () => {
              loadedImages.set(src, img)
              resolve()
            }

            img.onerror = error => {
              console.error('图片加载失败:', src, error)
              reject(error)
            }

            img.src = src
          })
        })

        try {
          await Promise.all(imagePromises)
        } catch (error) {
          console.error('部分图片加载失败:', error)
          throw error
        }
      },
      [loadedImages]
    )

    const render = useCallback(async () => {
      if (!ctx) return

      // 设置加载状态
      setIsLoading(true)
      onBeforeRender?.()

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight)

      // 分离图片和非图片元素
      const imageItems = renderList.filter(item => item.type === 'Image')

      try {
        // 先处理图片加载
        await loadImages(imageItems)

        // 所有图片加载完成后，按顺序渲染所有元素
        renderAllItems()
        onRenderSuccess?.()
      } catch (error) {
        onRenderError?.(error)
      } finally {
        setIsLoading(false)
        onRenderFinish?.()
      }
    }, [
      ctx,
      canvasWidth,
      canvasHeight,
      renderList,
      loadImages,
      renderAllItems,
      onBeforeRender,
      onRenderSuccess,
      onRenderError,
      onRenderFinish
    ])

    const initCanvas = useCallback(() => {
      const canvasElement = canvasRef.current
      if (!canvasElement) return

      setCanvas(canvasElement)
      const context = canvasElement.getContext('2d')
      setCtx(context)

      // 获取设备像素比
      const ratio = pixelRatio || window.devicePixelRatio || 1
      setActualPixelRatio(ratio)

      // 设置canvas实际尺寸（考虑设备像素比）
      const actualWidth = canvasWidth * ratio
      const actualHeight = canvasHeight * ratio

      // 设置canvas的实际像素尺寸
      canvasElement.width = actualWidth
      canvasElement.height = actualHeight

      // 缩放绘图上下文以匹配设备像素比
      context?.scale(ratio, ratio)

      // 设置图像渲染质量
      if (context) {
        context.imageSmoothingEnabled = true
        context.imageSmoothingQuality = 'high'
      }
    }, [canvasWidth, canvasHeight, pixelRatio])

    // 清空画布
    const clear = useCallback(() => {
      if (ctx) {
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
      }
    }, [ctx, canvasWidth, canvasHeight])

    // 获取当前设备像素比
    const getPixelRatio = useCallback(() => {
      return actualPixelRatio
    }, [actualPixelRatio])

    // 导出画布为图片
    const exportDataURL = useCallback(
      (type = 'image/png', quality = 1) => {
        if (!canvas) return null
        return canvas.toDataURL(type, quality)
      },
      [canvas]
    )

    // 导出画布为blob
    const exportBlobURL = useCallback(
      (type = 'image/png', quality = 1) => {
        return new Promise<Blob>((resolve, reject) => {
          if (!canvas) {
            reject(new Error('Canvas not initialized'))
            return
          }

          canvas.toBlob(
            blob => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('Failed to create blob'))
              }
            },
            type,
            quality
          )
        })
      },
      [canvas]
    )

    // 下载画布为图片
    const downloadImage = useCallback(
      async (filename = 'image', type = 'image/png', quality = 1) => {
        try {
          const blob = await exportBlobURL(type, quality)

          // 创建下载链接
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')

          // 设置文件名和扩展名
          const extension = type.split('/')[1] || 'png'
          const fullFilename = filename.includes('.')
            ? filename
            : `${filename}.${extension}`

          link.href = url
          link.download = fullFilename
          link.style.display = 'none'

          // 添加到DOM并触发下载
          document.body.appendChild(link)
          link.click()

          // 清理
          document.body.removeChild(link)
          URL.revokeObjectURL(url)

          return true
        } catch (error) {
          console.error('下载图片失败:', error)
          throw error
        }
      },
      [exportBlobURL]
    )

    // 初始化canvas
    useEffect(() => {
      initCanvas()
    }, [initCanvas])

    // 当renderList变化时重新渲染
    useEffect(() => {
      render()
    }, [render])

    // 当尺寸或像素比变化时重新初始化和渲染
    useEffect(() => {
      initCanvas()
      render()
    }, [width, height, pixelRatio])

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      clear,
      getPixelRatio,
      exportDataURL,
      exportBlobURL,
      downloadImage,
      render
    }))

    return (
      <div className="inline-block relative">
        <canvas
          ref={canvasRef}
          style={canvasStyle}
          className={`block max-w-full h-auto transition-opacity duration-300 ease-in-out ${isLoading ? 'opacity-70' : ''}`}
        />

        {/* Loading 遮罩层 */}
        {isLoading && (
          <div className="absolute top-0 left-0 right-0 bottom-0 bg-white/80 flex items-center justify-center z-10">
            <div className="text-center">
              <div className="w-8 h-8 border-[3px] border-gray-200 border-t-[3px] border-t-blue-500 rounded-full animate-spin mx-auto mb-3"></div>
              <div className="text-gray-600 text-sm font-medium">
                {loadingText}
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }
)

RenderCanvas.displayName = 'RenderCanvas'

export default RenderCanvas
