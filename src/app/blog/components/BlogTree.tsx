'use client'

import Link from 'next/link'
import { useState } from 'react'
import { ChevronDown, ChevronRight, Folder, FileText } from 'lucide-react'

interface FileNode {
  name: string
  path: string
  type: 'file' | 'directory'
  children?: FileNode[]
}

interface BlogTreeProps {
  nodes: FileNode[]
}

function FileTreeNode({ node, level = 0 }: { node: FileNode; level?: number }) {
  const [isExpanded, setIsExpanded] = useState(true)
  const indent = level * 24

  if (node.type === 'directory') {
    return (
      <div className="mb-2 relative">
        {/* 垂直虚线 */}
        {level > 0 && (
          <div
            className="absolute top-0 bottom-0 border-l border-dashed border-muted-foreground/30"
            style={{ left: `${indent - 12}px` }}
          />
        )}

        <div
          className="flex items-center mb-2 font-semibold text-lg cursor-pointer hover:bg-muted/20 p-2 rounded-md transition-colors relative"
          style={{ paddingLeft: `${indent}px` }}
          onClick={() => setIsExpanded(!isExpanded)}>
          {/* 水平虚线连接 */}
          {level > 0 && (
            <div
              className="absolute border-t border-dashed border-muted-foreground/30"
              style={{
                left: `${indent - 12}px`,
                width: '12px',
                top: '50%'
              }}
            />
          )}

          {isExpanded ? (
            <ChevronDown className="w-4 h-4 mr-2" />
          ) : (
            <ChevronRight className="w-4 h-4 mr-2" />
          )}
          <Folder className="w-4 h-4 mr-2" />
          <span className="capitalize">{node.name}</span>
        </div>

        {isExpanded && (
          <div className="relative">
            {node.children?.map((child, index) => (
              <FileTreeNode
                key={index}
                node={child}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      className="mb-2 relative"
      style={{ paddingLeft: `${indent}px` }}>
      {/* 垂直虚线 */}
      {level > 0 && (
        <div
          className="absolute top-0 bottom-0 border-l border-dashed border-muted-foreground/30"
          style={{ left: `${indent - 12}px` }}
        />
      )}

      <Link
        href={`/blog/${node.path}`}
        className="block">
        <div className="hover:bg-muted/20 p-2 rounded-md transition-colors cursor-pointer relative">
          {/* 水平虚线连接 */}
          {level > 0 && (
            <div
              className="absolute border-t border-dashed border-muted-foreground/30"
              style={{
                left: `${indent - 12}px`,
                width: '12px',
                top: '50%'
              }}
            />
          )}

          <div className="flex items-center text-base">
            <FileText className="w-4 h-4 mr-2" />
            {node.name}
          </div>
        </div>
      </Link>
    </div>
  )
}

export function BlogTree({ nodes }: BlogTreeProps) {
  return (
    <div className="space-y-2">
      {nodes.map((node, index) => (
        <FileTreeNode
          key={index}
          node={node}
        />
      ))}
    </div>
  )
}
