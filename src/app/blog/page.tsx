import { promises as fs } from 'fs'
import path from 'path'
import { BlogTree } from './components/BlogTree'

interface FileNode {
  name: string
  path: string
  type: 'file' | 'directory'
  children?: FileNode[]
}

async function getContentStructure(): Promise<FileNode[]> {
  const contentDir = path.join(process.cwd(), 'src/content')

  async function buildTree(
    dirPath: string,
    relativePath: string = ''
  ): Promise<FileNode[]> {
    const items = await fs.readdir(dirPath, { withFileTypes: true })
    const nodes: FileNode[] = []

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name)
      const itemRelativePath = relativePath
        ? `${relativePath}/${item.name}`
        : item.name

      if (item.isDirectory()) {
        const children = await buildTree(itemPath, itemRelativePath)
        nodes.push({
          name: item.name,
          path: itemRelativePath,
          type: 'directory',
          children
        })
      } else if (item.name.endsWith('.mdx')) {
        nodes.push({
          name: item.name.replace('.mdx', ''),
          path: itemRelativePath.replace('.mdx', ''),
          type: 'file'
        })
      }
    }

    // 排序：目录在前，文件在后，然后按名称排序
    return nodes.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })
  }

  return buildTree(contentDir)
}

export default async function BlogPage() {
  const contentStructure = await getContentStructure()

  return (
    <div className="max-w-4xl mx-auto px-6 py-16">
      <div className="mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
          博客文章
        </h1>
        <p className="text-xl text-gray-700 font-serif leading-relaxed">
          探索我的思考与分享，记录学习与成长的点点滴滴
        </p>
      </div>

      <div className="bg-white">
        <BlogTree nodes={contentStructure} />
      </div>
    </div>
  )
}
