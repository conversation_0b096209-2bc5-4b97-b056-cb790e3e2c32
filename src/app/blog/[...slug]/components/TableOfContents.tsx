'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { ChevronRight, ChevronDown, ChevronUp } from 'lucide-react'

interface TocItem {
  id: string
  text: string
  level: number
  children?: TocItem[]
}

interface TableOfContentsProps {
  depth?: number // 控制渲染深度，默认为3（h2, h3, h4）
}

export function TableOfContents({
  depth: propDepth = 3
}: TableOfContentsProps) {
  const [toc, setToc] = useState<TocItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [isScrolling, setIsScrolling] = useState(false) // 添加滚动状态标记
  const router = useRouter()

  // 构建层级结构的函数
  const buildHierarchy = (flatItems: TocItem[]): TocItem[] => {
    const result: TocItem[] = []
    const stack: TocItem[] = []

    flatItems.forEach(item => {
      const newItem = { ...item, children: [] }

      // 找到合适的父级
      while (
        stack.length > 0 &&
        stack[stack.length - 1].level >= newItem.level
      ) {
        stack.pop()
      }

      if (stack.length === 0) {
        result.push(newItem)
      } else {
        const parent = stack[stack.length - 1]
        if (!parent.children) parent.children = []
        parent.children.push(newItem)
      }

      stack.push(newItem)
    })

    return result
  }

  useEffect(() => {
    // 从data属性或props获取深度设置
    const depthElement = document.querySelector('[data-toc-depth]')
    const dataDepth = depthElement?.getAttribute('data-toc-depth')
    const depth = dataDepth ? parseInt(dataDepth) : propDepth

    // 提取标题
    const headings = document.querySelectorAll('h2, h3, h4, h5, h6')
    const flatItems: TocItem[] = []

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))

      // 根据depth参数过滤标题级别
      if (level >= 2 && level <= depth + 1) {
        let id = heading.id

        // 如果没有id，生成一个
        if (!id) {
          id =
            heading.textContent
              ?.toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
              .trim() || `heading-${index}`
          heading.id = id
        }

        flatItems.push({
          id,
          text: heading.textContent || '',
          level
        })
      }
    })

    // 构建层级结构
    const hierarchicalToc = buildHierarchy(flatItems)
    setToc(hierarchicalToc)

    // 默认展开所有项目
    const allIds = new Set<string>()
    const collectIds = (items: TocItem[]) => {
      items.forEach(item => {
        if (item.children && item.children.length > 0) {
          allIds.add(item.id)
          collectIds(item.children)
        }
      })
    }
    collectIds(hierarchicalToc)
    setExpandedItems(allIds)
  }, [propDepth])

  useEffect(() => {
    // 基于滚动位置计算当前活跃标题
    const handleScroll = () => {
      if (isScrolling) return // 程序化滚动时不更新

      // 递归收集所有ID和位置
      const collectAllIds = (
        items: TocItem[]
      ): Array<{ id: string; element: HTMLElement; top: number }> => {
        const result: Array<{ id: string; element: HTMLElement; top: number }> =
          []
        items.forEach(item => {
          const element = document.getElementById(item.id)
          if (element) {
            result.push({
              id: item.id,
              element,
              top: element.getBoundingClientRect().top + window.pageYOffset
            })
          }
          if (item.children) {
            result.push(...collectAllIds(item.children))
          }
        })
        return result
      }

      const headings = collectAllIds(toc)
      if (headings.length === 0) return

      // 按位置排序
      headings.sort((a, b) => a.top - b.top)

      // 找到当前应该高亮的标题
      const scrollTop = window.pageYOffset + 120 // 考虑头部偏移
      let activeHeading = headings[0]

      for (const heading of headings) {
        if (heading.top <= scrollTop) {
          activeHeading = heading
        } else {
          break
        }
      }

      setActiveId(activeHeading.id)
    }

    // 防抖处理
    let scrollTimer: NodeJS.Timeout
    const debouncedHandleScroll = () => {
      clearTimeout(scrollTimer)
      scrollTimer = setTimeout(handleScroll, 10)
    }

    window.addEventListener('scroll', debouncedHandleScroll)
    handleScroll() // 初始调用

    return () => {
      window.removeEventListener('scroll', debouncedHandleScroll)
      clearTimeout(scrollTimer)
    }
  }, [toc, isScrolling])

  // 监听URL hash变化并设置活跃状态
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) // 移除 # 符号
      if (hash) {
        setActiveId(hash)
      }
    }

    // 页面加载时检查hash
    handleHashChange()

    // 监听hash变化
    window.addEventListener('hashchange', handleHashChange)

    return () => {
      window.removeEventListener('hashchange', handleHashChange)
    }
  }, [])

  // 页面加载完成后，如果URL中有hash，自动滚动到对应位置
  useEffect(() => {
    if (toc.length > 0) {
      const hash = window.location.hash.slice(1)
      if (hash) {
        // 延迟一下确保DOM已经渲染完成
        setTimeout(() => {
          // 直接设置活跃状态和滚动，不使用scrollToHeading避免重复的URL更新
          setActiveId(hash)
          setIsScrolling(true)

          const element = document.getElementById(hash)
          if (element) {
            const headerOffset = 100
            const elementPosition = element.getBoundingClientRect().top
            const offsetPosition =
              elementPosition + window.pageYOffset - headerOffset

            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth'
            })

            // 简单的超时重置
            setTimeout(() => setIsScrolling(false), 800)
          } else {
            setIsScrolling(false)
          }
        }, 300)
      }
    }
  }, [toc])

  const scrollToHeading = (id: string) => {
    // 立即设置活跃状态，防止被覆盖
    setActiveId(id)

    // 更新URL hash
    const currentUrl = new URL(window.location.href)
    currentUrl.hash = id
    window.history.replaceState(null, '', currentUrl.toString())

    // 标记开始程序化滚动
    setIsScrolling(true)

    // 滚动到目标元素
    const element = document.getElementById(id)

    if (element) {
      // 使用更精确的滚动方法
      const headerOffset = 100 // 考虑固定头部的高度
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset

      // 如果距离很近，直接跳转不使用动画
      if (Math.abs(elementPosition) < 50) {
        window.scrollTo({
          top: offsetPosition,
          behavior: 'auto'
        })
        setIsScrolling(false)
      } else {
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        })

        // 监听滚动完成
        let scrollEndTimer: NodeJS.Timeout
        const checkScrollEnd = () => {
          clearTimeout(scrollEndTimer)
          scrollEndTimer = setTimeout(() => {
            setIsScrolling(false)
            window.removeEventListener('scroll', checkScrollEnd)
          }, 100)
        }

        window.addEventListener('scroll', checkScrollEnd)
        checkScrollEnd() // 立即执行一次

        // 保险起见，最多800ms后强制重置
        setTimeout(() => {
          setIsScrolling(false)
          window.removeEventListener('scroll', checkScrollEnd)
          clearTimeout(scrollEndTimer)
        }, 800)
      }
    } else {
      setIsScrolling(false)
    }
  }

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }

  // 递归渲染目录项
  const renderTocItem = (item: TocItem, level: number = 0): React.ReactNode => {
    const isActive = activeId === item.id
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.has(item.id)
    const indent = level * 16

    return (
      <div
        key={item.id}
        className="w-full">
        <div className="flex items-center w-full">
          {/* 展开/收起按钮 */}
          {hasChildren && (
            <button
              onClick={e => {
                e.stopPropagation()
                toggleExpanded(item.id)
              }}
              className="flex-shrink-0 p-1 hover:bg-muted/20 rounded transition-colors"
              style={{ marginLeft: `${indent}px` }}>
              <ChevronRight
                className={`w-3 h-3 transition-transform duration-200 ${
                  isExpanded ? 'rotate-90' : ''
                }`}
              />
            </button>
          )}

          {/* 标题按钮 */}
          <button
            onClick={() => scrollToHeading(item.id)}
            className={`
              flex-1 text-left text-sm transition-colors duration-200 py-1 px-2 rounded
              hover:text-foreground hover:bg-muted/10
              ${
                isActive
                  ? 'text-foreground font-medium bg-primary/10 border-l-2 border-primary'
                  : 'text-muted-foreground'
              }
            `}
            style={{
              marginLeft: hasChildren ? '0px' : `${indent + 16}px`
            }}>
            <span className="truncate">{item.text}</span>
          </button>
        </div>

        {/* 子项目 */}
        {hasChildren && isExpanded && (
          <div className="mt-1">
            {item.children!.map(child => renderTocItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  if (toc.length === 0) return null

  return (
    <div className="fixed left-4 top-1/2 transform -translate-y-1/2 w-64 max-h-[70vh] hidden xl:block">
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg shadow-lg overflow-hidden">
        {/* 目录标题栏 */}
        <div className="p-4 border-b border-border">
          <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
            目录
          </h3>
        </div>

        {/* 目录内容 */}
        <nav className="p-4 space-y-1 max-h-[60vh] overflow-y-auto">
          {toc.map(item => renderTocItem(item))}
        </nav>
      </div>
    </div>
  )
}
