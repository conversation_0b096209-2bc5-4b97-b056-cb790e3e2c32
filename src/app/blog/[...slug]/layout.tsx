import { TableOfContents } from './components/TableOfContents'
import { BackToTop } from './components/BackToTop'

export default function MdxLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="bg-white relative">
      {/* 侧边栏目录 */}
      <TableOfContents />

      {/* Dario Amodei 风格布局 - 学术专业 */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <article className="w-full">
          {/* 文章内容 - 专业展示 */}
          <div className="prose prose-lg max-w-none">{children}</div>

          {/* 学术风格底部 */}
          <div className="mt-16 pt-8 border-t border-gray-200">
            <div className="flex justify-between items-center text-sm text-gray-600">
              <BackToTop />
              <p>个人观点，仅供参考</p>
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}
