import { TableOfContents } from './components/TableOfContents'
import { BackToTop } from './components/BackToTop'

export default function MdxLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-background relative">
      {/* 侧边栏目录 */}
      <TableOfContents />

      {/* Dario Amodei 风格布局 - 学术专业 */}
      <div className="max-w-4xl mx-auto px-6 py-12 md:py-16">
        <article className="w-full">
          {/* 文章内容 - 专业展示 */}
          <div className="prose prose-lg max-w-none">{children}</div>

          {/* 学术风格底部 */}
          <div className="mt-16 pt-8 border-t border-border">
            <div className="flex justify-between items-center text-sm text-muted-foreground">
              <BackToTop />
              <p>个人观点</p>
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}
