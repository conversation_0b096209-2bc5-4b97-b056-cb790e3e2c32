import { Metadata } from 'next'
import { promises as fs } from 'fs'
import path from 'path'

// https://nextjs.org/docs/app/guides/mdx#using-dynamic-imports
export default async function Page({
  params,
  searchParams
}: {
  params: Promise<{ slug: string[] }>
  searchParams: Promise<{ depth?: string }>
}) {
  const { slug } = await params
  const { depth } = await searchParams

  const { default: Post } = await import(`@/content/${slug.join('/')}.mdx`)

  // 将深度参数传递给布局（通过context或其他方式）
  return (
    <div data-toc-depth={depth || '3'}>
      <Post />
    </div>
  )
}

async function getAllMdxFiles(): Promise<string[]> {
  const contentDir = path.join(process.cwd(), 'src/content')
  const files: string[] = []

  async function scanDirectory(
    dirPath: string,
    relativePath: string = ''
  ): Promise<void> {
    const items = await fs.readdir(dirPath, { withFileTypes: true })

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name)
      const itemRelativePath = relativePath
        ? `${relativePath}/${item.name}`
        : item.name

      if (item.isDirectory()) {
        await scanDirectory(itemPath, itemRelativePath)
      } else if (item.name.endsWith('.mdx')) {
        files.push(itemRelativePath.replace('.mdx', ''))
      }
    }
  }

  await scanDirectory(contentDir)
  return files
}

export async function generateStaticParams() {
  const files = await getAllMdxFiles()
  return files.map(file => ({ slug: file.split('/') }))
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string[] }>
}): Promise<Metadata> {
  const { slug } = await params
  const slugPath = slug.join('/')

  // 尝试读取文件内容来生成更好的元数据
  let title = '文章'
  let description = '深度思考与个人见解'

  try {
    const contentPath = path.join(
      process.cwd(),
      'src/content',
      `${slugPath}.mdx`
    )
    const content = await fs.readFile(contentPath, 'utf-8')

    // 提取第一行作为标题（如果是 # 开头）
    const lines = content.split('\n')
    const firstLine = lines[0]?.trim()
    if (firstLine?.startsWith('# ')) {
      title = firstLine.replace('# ', '')
    } else {
      // 使用文件名作为标题
      const fileName = slug[slug.length - 1] || slugPath
      title = fileName.replace(/[-_]/g, ' ')
    }

    // 提取前几行作为描述
    const textContent = content
      .replace(/^#.*$/gm, '') // 移除标题
      .replace(/\n+/g, ' ') // 合并换行
      .trim()
      .substring(0, 160)

    if (textContent) {
      description = textContent + '...'
    }
  } catch (error) {
    // 如果读取失败，使用默认值
    console.warn(`Failed to read metadata for ${slugPath}:`, error)
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article'
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description
    }
  }
}

export const dynamicParams = true
