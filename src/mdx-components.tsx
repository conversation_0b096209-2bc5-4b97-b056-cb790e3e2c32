import type { MDXComponents } from 'mdx/types'
import Image, { ImageProps } from 'next/image'
import React from 'react'

// This file allows you to provide custom React components
// to be used in MDX files. You can import and use any
// React component you want, including inline styles,
// components from other libraries, and more.

// 生成标题ID的工具函数
function generateId(text: string | React.ReactNode): string {
  // 如果是React节点，提取文本内容
  let textContent = ''
  if (typeof text === 'string') {
    textContent = text
  } else if (React.isValidElement(text)) {
    textContent = extractTextFromReactNode(text)
  } else if (Array.isArray(text)) {
    textContent = text
      .map(item =>
        typeof item === 'string' ? item : extractTextFromReactNode(item)
      )
      .join('')
  }

  return textContent
    .toLowerCase()
    .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 支持中文字符
    .replace(/\s+/g, '-')
    .trim()
}

// 从React节点中提取文本内容
function extractTextFromReactNode(node: React.ReactNode): string {
  if (typeof node === 'string') return node
  if (typeof node === 'number') return node.toString()
  if (Array.isArray(node)) {
    return node.map(extractTextFromReactNode).join('')
  }
  if (React.isValidElement(node) && node.props.children) {
    return extractTextFromReactNode(node.props.children)
  }
  return ''
}

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    h1: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h1
          id={id}
          className="text-4xl md:text-5xl font-bold text-foreground mb-8 leading-tight scroll-mt-20">
          {children}
        </h1>
      )
    },
    h2: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h2
          id={id}
          className="text-2xl md:text-3xl font-bold text-foreground mb-6 mt-12 leading-tight scroll-mt-20">
          {children}
        </h2>
      )
    },
    h3: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h3
          id={id}
          className="text-xl md:text-2xl font-semibold text-foreground mb-4 mt-8 leading-tight scroll-mt-20">
          {children}
        </h3>
      )
    },
    h4: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h4
          id={id}
          className="text-lg md:text-xl font-medium text-foreground mb-3 mt-6 leading-tight scroll-mt-20">
          {children}
        </h4>
      )
    },
    h5: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h5
          id={id}
          className="text-base md:text-lg font-medium text-foreground mb-2 mt-4 leading-tight scroll-mt-20">
          {children}
        </h5>
      )
    },
    h6: ({ children }) => {
      const id = typeof children === 'string' ? generateId(children) : ''
      return (
        <h6
          id={id}
          className="text-sm md:text-base font-medium text-muted-foreground mb-2 mt-3 leading-tight scroll-mt-20">
          {children}
        </h6>
      )
    },

    // 学术风格段落 - 适中大小，良好间距
    p: ({ children }) => (
      <p className="text-base md:text-lg text-foreground mb-6 leading-relaxed">
        {children}
      </p>
    ),

    // 专业的图片样式
    img: props => (
      <div className="my-8">
        <Image
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 80vw, 70vw"
          className="w-full h-auto rounded border border-border"
          {...(props as ImageProps)}
        />
      </div>
    ),

    // 学术风格列表
    ul: ({ children }) => (
      <ul className="mb-6 space-y-2 pl-6 list-disc">{children}</ul>
    ),
    ol: ({ children }) => (
      <ol className="mb-6 space-y-2 pl-6 list-decimal">{children}</ol>
    ),
    li: ({ children }) => (
      <li className="text-base md:text-lg text-foreground leading-relaxed">
        {children}
      </li>
    ),

    // 学术引用样式
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-muted-foreground/40 pl-6 my-6 text-base md:text-lg italic text-muted-foreground bg-muted/20 py-4 rounded-r">
        {children}
      </blockquote>
    ),

    // 专业代码样式
    code: ({ children, ...props }) => (
      <code
        className="bg-muted px-2 py-1 rounded text-sm font-mono text-foreground border"
        {...props}>
        {children}
      </code>
    ),
    pre: ({ children }) => (
      <pre className="bg-muted p-4 rounded border overflow-x-auto my-6 text-sm font-mono">
        {children}
      </pre>
    ),

    // 学术风格链接
    a: ({ children, ...props }) => (
      <a
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline underline-offset-2 transition-colors"
        {...props}>
        {children}
      </a>
    ),

    // 学术分割线
    hr: () => <hr className="my-12 border-border" />,

    // 专业表格样式
    table: ({ children }) => (
      <div className="my-6 overflow-x-auto">
        <table className="w-full border-collapse text-sm border border-border rounded">
          {children}
        </table>
      </div>
    ),
    th: ({ children }) => (
      <th className="border border-border px-4 py-3 bg-muted font-semibold text-left">
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className="border border-border px-4 py-3">{children}</td>
    ),

    // 强调文本
    strong: ({ children }) => (
      <strong className="font-semibold text-foreground">{children}</strong>
    ),
    em: ({ children }) => <em className="italic">{children}</em>,

    ...components
  }
}
