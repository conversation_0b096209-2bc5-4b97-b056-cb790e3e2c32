{"name": "one-web-linknote", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "npm run format:lint && npm run format:pretty", "format:lint": "eslint src --ext .js,.jsx,.cjs,.mjs,.ts,.tsx --fix --config eslint.config.mjs", "format:pretty": "prettier . --write"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.4", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.2", "next-themes": "^0.4.6", "puppeteer": "^24.14.0", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint/eslintrc": "^3", "@oneyoung/pino-cli": "^1.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "lint-staged": "^11.1.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "yorkie-pnpm": "^2.0.1"}, "packageManager": "pnpm@9.15.1+sha1.abc117858086cb10ecb54828d180b035cb6c8fdd", "gitHooks": {"commit-msg": "commitlint --edit", "pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,cjs,mjs,ts,tsx}": "eslint --fix --config eslint.config.mjs"}}