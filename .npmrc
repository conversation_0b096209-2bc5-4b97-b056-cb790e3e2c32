registry=https://registry.npmjs.org
@oneyoung:registry=https://registry.npmjs.org

# These are the options for the workspace feature, especially for pnpm.
# 将本地包连接到node_modules
link-workspace-packages=true
# 优先使用工作目录的package，只有save-workspace-protocal=false时有效
prefer-workspace-packages=true
# 共享lock文件，有利于模块解析
shared-workspace-lockfile=true
# 用来控制依赖在package.json中的版本范围
save-workspace-protocol=true
# 递归执行是否作用在根工作区
include-workspace-root=false
# 忽略工作区根目录检查
ignore-workspace-root-check=true
